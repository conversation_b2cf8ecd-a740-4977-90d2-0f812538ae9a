{"name": "naroop-social-platform", "version": "1.0.0", "description": "Naroop - A social media platform where Black people share positive stories and celebrate community", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "node server.js", "prod": "node server.js", "logs": "type logs\\naroop.log", "setup": "npm install && node scripts/setup.js", "health": "node -e \"const port = process.env.PORT || 3000; require('http').get(`http://localhost:${port}/health`, res => { let data = ''; res.on('data', chunk => data += chunk); res.on('end', () => console.log(data)); })\"", "live": "live-server . --port=${PORT:-3000}"}, "dependencies": {"body-parser": "^1.20.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "express": "^4.18.2", "firebase": "^11.10.0", "firebase-admin": "^13.4.0"}, "optionalDependencies": {"compression": "^1.7.4", "dotenv": "^16.3.1", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0"}, "devDependencies": {"live-server": "^1.2.2", "nodemon": "^3.1.10"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "keywords": ["social-media", "black-community", "positive-stories", "storytelling", "community", "naroop"], "author": "<PERSON>", "license": "MIT"}